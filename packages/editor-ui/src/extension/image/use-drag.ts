import { MAX_IMAGE_HEIGHT, MIN_IMAGE_WIDTH } from '@repo/editor-common';
import { useCallback, useEffect, useRef, useState } from 'react';

interface UseDragProps {
  imageDisplayWidth?: number;
  imageDisplayHeight?: number;
  maxImageHeight?: number;
  updateAttributes: (attributes: any) => void;
}

export function useDrag({
  imageDisplayWidth,
  imageDisplayHeight,
  maxImageHeight,
  updateAttributes,
}: UseDragProps) {
  const [dragSide, setDragSide] = useState<'left' | 'right'>('right');
  const [isDragging, setIsDragging] = useState(false);
  const animationFrameRef = useRef<number | null>(null);
  const [initialWidth, setInitialWidth] = useState(0);
  const [initialX, setInitialX] = useState(0);
  const [displayRatio, setDisplayRatio] = useState(
    imageDisplayWidth && imageDisplayHeight ? imageDisplayWidth / imageDisplayHeight : 1,
  );
  const lastMousePosRef = useRef<number>(0);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);

  const onDragImageSize = useCallback((isLeft: boolean, e: React.MouseEvent) => {
    if (!imageContainerRef.current) return;

    if (animationFrameRef.current !== null) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    const currentWidth = imageContainerRef.current.offsetWidth;
    setInitialWidth(currentWidth);
    setInitialX(e.clientX);
    lastMousePosRef.current = e.clientX;
    setDragSide(isLeft ? 'left' : 'right');
    setIsDragging(true);

    e.preventDefault();
  }, []);

  useEffect(() => {
    if (!isDragging) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !imageContainerRef.current) return;

      lastMousePosRef.current = e.clientX;

      if (animationFrameRef.current === null) {
        animationFrameRef.current = requestAnimationFrame(() => {
          const deltaX = lastMousePosRef.current - initialX;
          let newWidth = initialWidth;

          if (dragSide === 'left') {
            newWidth = Math.max(MIN_IMAGE_WIDTH, initialWidth - deltaX);
          } else {
            newWidth = Math.max(MIN_IMAGE_WIDTH, initialWidth + deltaX);
          }

          if (imageContainerRef.current?.parentElement) {
            newWidth = Math.min(newWidth, imageContainerRef.current?.parentElement?.offsetWidth);
          }

          const newHeight = newWidth / displayRatio;

          if (imageContainerRef.current) {
            imageContainerRef.current.style.width = `${newWidth}px`;
            imageContainerRef.current.style.height = `${newHeight}px`;
            imageContainerRef.current.style.aspectRatio = displayRatio.toString();
          }

          animationFrameRef.current = null;
        });
      }

      e.preventDefault();
    };

    const handleMouseUp = () => {
      if (isDragging && imageContainerRef.current) {
        setIsDragging(false);

        if (animationFrameRef.current !== null) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }

        const newWidth = parseInt(imageContainerRef.current.style.width);
        let newHeight = newWidth / displayRatio;

        // 再次检查高度是否超过最大限制
        if (newHeight > (maxImageHeight ?? MAX_IMAGE_HEIGHT)) {
          newHeight = maxImageHeight ?? MAX_IMAGE_HEIGHT;
          const adjustedWidth = newHeight * displayRatio;
          updateAttributes({
            width: adjustedWidth,
            height: newHeight,
          });
        } else {
          updateAttributes({
            width: newWidth,
            height: newHeight,
          });
        }
      }
    };

    document.addEventListener('mousemove', handleMouseMove, {
      passive: false,
    });
    document.addEventListener('mouseup', handleMouseUp, { passive: true });

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [
    isDragging,
    initialWidth,
    initialX,
    dragSide,
    updateAttributes,
    displayRatio,
    maxImageHeight,
  ]);

  return {
    isDragging,
    dragSide,
    displayRatio,
    setDisplayRatio,
    imageContainerRef,
    onDragImageSize,
  };
}